using AssistantService.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;

namespace AssistantService.Extensions;

/// <summary>
/// Extension methods for configuring Supabase JWT authentication
/// </summary>
public static class SupabaseJwtExtensions
{
    /// <summary>
    /// Adds Supabase JWT authentication with auto-discovery and enhanced error handling
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddSupabaseJwtAuthentication(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        // Check if we're in a testing environment
        var environment = configuration["ASPNETCORE_ENVIRONMENT"];
        var isTestEnvironment = string.Equals(environment, "Testing", StringComparison.OrdinalIgnoreCase) ||
                               string.Equals(environment, "Test", StringComparison.OrdinalIgnoreCase);

        if (isTestEnvironment)
        {
            // Skip JWT configuration in test environment
            return services;
        }

        // Validate required configuration early to provide clear error messages
        var supabaseUrl = configuration["SUPABASE_URL"];
        var supabaseProjectId = configuration["SUPABASE_PROJECT_ID"];

        if (string.IsNullOrEmpty(supabaseUrl))
        {
            throw new InvalidOperationException(
                "SUPABASE_URL must be configured for JWT authentication. " +
                "Set this in your environment variables or appsettings.json");
        }

        if (string.IsNullOrEmpty(supabaseProjectId))
        {
            throw new InvalidOperationException(
                "SUPABASE_PROJECT_ID must be configured for JWT authentication. " +
                "Set this in your environment variables or appsettings.json");
        }

        // Add HTTP client factory for JWKS requests
        services.AddHttpClient();

        // Register the JWKS key cache as singleton
        services.AddSingleton<JwksKeyCache>();

        // Register the background service for JWKS cache management
        services.AddHostedService<JwksBackgroundService>();

        // Register the custom JWT configuration
        services.AddSingleton<SupabaseJwtConfiguration>();
        services.ConfigureOptions<SupabaseJwtConfiguration>();

        // Add JWT Bearer authentication
        services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(); // Configuration will be handled by SupabaseJwtConfiguration

        return services;
    }

    /// <summary>
    /// Adds Supabase JWT authentication with custom options
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <param name="configureOptions">Additional JWT Bearer options configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddSupabaseJwtAuthentication(
        this IServiceCollection services,
        IConfiguration configuration,
        Action<JwtBearerOptions> configureOptions)
    {
        services.AddSupabaseJwtAuthentication(configuration);
        services.Configure<JwtBearerOptions>(JwtBearerDefaults.AuthenticationScheme, configureOptions);
        return services;
    }

    /// <summary>
    /// Validates that all required Supabase configuration is present
    /// </summary>
    /// <param name="configuration">The configuration to validate</param>
    /// <returns>True if configuration is valid, false otherwise</returns>
    public static bool HasValidSupabaseConfiguration(this IConfiguration configuration)
    {
        var supabaseUrl = configuration["SUPABASE_URL"];
        var supabaseProjectId = configuration["SUPABASE_PROJECT_ID"];
        
        return !string.IsNullOrEmpty(supabaseUrl) && !string.IsNullOrEmpty(supabaseProjectId);
    }

    /// <summary>
    /// Gets the Supabase configuration summary for logging with sensitive data redacted
    /// </summary>
    /// <param name="configuration">The configuration</param>
    /// <returns>Configuration summary object with redacted sensitive values</returns>
    public static object GetSupabaseConfigurationSummary(this IConfiguration configuration)
    {
        var supabaseUrl = configuration["SUPABASE_URL"];
        var projectId = configuration["SUPABASE_PROJECT_ID"];

        return new
        {
            SupabaseUrl = !string.IsNullOrEmpty(supabaseUrl)
                ? $"{new Uri(supabaseUrl).Host}/**REDACTED**"
                : null,
            ProjectId = !string.IsNullOrEmpty(projectId)
                ? $"{projectId[..Math.Min(8, projectId.Length)]}***"
                : null,
            HasAnonKey = !string.IsNullOrEmpty(configuration["SUPABASE_ANON_KEY"]),
            HasServiceRoleKey = !string.IsNullOrEmpty(configuration["SUPABASE_SERVICE_ROLE_KEY"]),
            Environment = configuration["ASPNETCORE_ENVIRONMENT"]
        };
    }
}
